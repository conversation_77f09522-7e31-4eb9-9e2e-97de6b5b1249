{"actions": [], "autoname": "ACC-PCV-.YYYY.-.#####", "creation": "2013-01-10 16:34:07", "doctype": "DocType", "engine": "InnoDB", "field_order": ["transaction_date", "company", "fiscal_year", "period_start_date", "period_end_date", "amended_from", "column_break1", "closing_account_head", "gle_processing_status", "remarks", "error_message"], "fields": [{"default": "Today", "fieldname": "transaction_date", "fieldtype": "Date", "label": "Transaction Date", "oldfieldname": "transaction_date", "oldfieldtype": "Date"}, {"fieldname": "fiscal_year", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Fiscal Year", "oldfieldname": "fiscal_year", "oldfieldtype": "Select", "options": "Fiscal Year", "reqd": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "oldfieldname": "amended_from", "oldfieldtype": "Data", "options": "Period Closing Voucher", "read_only": 1}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "oldfieldname": "company", "oldfieldtype": "Select", "options": "Company", "reqd": 1}, {"fieldname": "column_break1", "fieldtype": "Column Break", "oldfieldtype": "Column Break"}, {"description": "The account head under Liability or Equity, in which Profit/Loss will be booked", "fieldname": "closing_account_head", "fieldtype": "Link", "label": "Closing Account Head", "oldfieldname": "closing_account_head", "oldfieldtype": "Link", "options": "Account", "reqd": 1}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks", "oldfieldname": "remarks", "oldfieldtype": "Small Text", "reqd": 1}, {"depends_on": "eval:doc.docstatus!=0", "fieldname": "gle_processing_status", "fieldtype": "Select", "label": "GL Entry Processing Status", "options": "In Progress\nCompleted\nFailed", "read_only": 1}, {"depends_on": "eval:doc.gle_processing_status=='Failed'", "fieldname": "error_message", "fieldtype": "Text", "label": "Error Message", "read_only": 1}, {"fieldname": "period_end_date", "fieldtype": "Date", "label": "Period End Date", "reqd": 1}, {"fieldname": "period_start_date", "fieldtype": "Date", "label": "Period Start Date", "oldfieldname": "posting_date", "oldfieldtype": "Date", "reqd": 1}], "icon": "fa fa-file-text", "idx": 1, "is_submittable": 1, "links": [], "modified": "2024-09-15 17:22:45.291628", "modified_by": "Administrator", "module": "Accounts", "name": "Period Closing Voucher", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "submit": 1, "write": 1}], "search_fields": "fiscal_year, period_start_date, period_end_date", "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "closing_account_head"}