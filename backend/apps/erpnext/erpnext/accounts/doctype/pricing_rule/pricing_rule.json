{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "naming_series:", "creation": "2014-02-21 15:02:51", "doctype": "DocType", "engine": "InnoDB", "field_order": ["applicability_section", "naming_series", "title", "disable", "apply_on", "price_or_product_discount", "warehouse", "column_break_7", "items", "item_groups", "brands", "mixed_conditions", "is_cumulative", "coupon_code_based", "section_break_18", "apply_rule_on_other", "column_break_17", "other_item_code", "other_item_group", "other_brand", "section_break_7", "selling", "buying", "column_break_11", "applicable_for", "customer", "customer_group", "territory", "sales_partner", "campaign", "supplier", "supplier_group", "section_break_19", "min_qty", "max_qty", "column_break_21", "min_amt", "max_amt", "product_discount_scheme_section", "same_item", "free_item", "free_qty", "free_item_rate", "column_break_42", "free_item_uom", "round_free_qty", "dont_enforce_free_item_qty", "is_recursive", "recurse_for", "apply_recursion_over", "section_break_23", "valid_from", "valid_upto", "col_break1", "company", "currency", "margin", "margin_type", "column_break_33", "margin_rate_or_amount", "price_discount_scheme_section", "rate_or_discount", "apply_discount_on", "col_break2", "rate", "discount_amount", "discount_percentage", "for_price_list", "dynamic_condition_tab", "condition", "section_break_13", "apply_multiple_pricing_rules", "apply_discount_on_rate", "column_break_66", "threshold_percentage", "validate_pricing_rule_section", "validate_applied_rule", "column_break_texp", "rule_description", "priority_section", "has_priority", "column_break_sayg", "priority", "help_section", "pricing_rule_help", "reference_section", "promotional_scheme_id", "promotional_scheme"], "fields": [{"fieldname": "applicability_section", "fieldtype": "Section Break"}, {"fieldname": "title", "fieldtype": "Data", "label": "Title", "no_copy": 1, "reqd": 1}, {"default": "0", "fieldname": "disable", "fieldtype": "Check", "label": "Disable"}, {"default": "Item Code", "fieldname": "apply_on", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Apply On", "options": "\nItem Code\nItem Group\nBrand\nTransaction", "reqd": 1}, {"fieldname": "price_or_product_discount", "fieldtype": "Select", "label": "Price or Product Discount", "options": "Price\nProduct", "reqd": 1}, {"depends_on": "eval:doc.apply_on != 'Transaction'", "fieldname": "warehouse", "fieldtype": "Link", "label": "Warehouse", "options": "Warehouse", "search_index": 1}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.apply_on == 'Item Code'", "fieldname": "items", "fieldtype": "Table", "label": "Apply Rule On Item Code", "options": "Pricing Rule Item Code"}, {"depends_on": "eval:doc.apply_on == 'Item Group'", "fieldname": "item_groups", "fieldtype": "Table", "label": "Apply Rule On Item Group", "options": "Pricing Rule Item Group"}, {"depends_on": "eval:doc.apply_on == 'Brand'", "fieldname": "brands", "fieldtype": "Table", "label": "Apply Rule On Brand", "options": "Pricing Rule Brand"}, {"default": "0", "depends_on": "eval:doc.apply_on != 'Transaction'", "description": "Conditions will be applied on all the selected items combined. ", "fieldname": "mixed_conditions", "fieldtype": "Check", "label": "Mixed Conditions"}, {"default": "0", "fieldname": "is_cumulative", "fieldtype": "Check", "label": "Is Cumulative"}, {"default": "0", "fieldname": "coupon_code_based", "fieldtype": "Check", "label": "Coupon Code Based"}, {"collapsible": 1, "depends_on": "eval:doc.apply_on != 'Transaction' && !doc.mixed_conditions", "fieldname": "section_break_18", "fieldtype": "Section Break", "label": "Discount on Other Item"}, {"fieldname": "apply_rule_on_other", "fieldtype": "Select", "label": "Apply Rule On Other", "options": "\nItem Code\nItem Group\nBrand"}, {"fieldname": "column_break_17", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.apply_rule_on_other == 'Item Code'", "fieldname": "other_item_code", "fieldtype": "Link", "label": "Item Code", "options": "<PERSON><PERSON>"}, {"depends_on": "eval:doc.apply_rule_on_other == 'Item Group'", "fieldname": "other_item_group", "fieldtype": "Link", "label": "Item Group", "options": "Item Group"}, {"depends_on": "eval:doc.apply_rule_on_other == 'Brand'", "fieldname": "other_brand", "fieldtype": "Link", "label": "Brand", "options": "Brand"}, {"fieldname": "section_break_7", "fieldtype": "Section Break", "label": "Party Information"}, {"default": "0", "fieldname": "selling", "fieldtype": "Check", "label": "Selling"}, {"default": "0", "fieldname": "buying", "fieldtype": "Check", "label": "Buying"}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"depends_on": "eval: doc.buying || doc.selling", "fieldname": "applicable_for", "fieldtype": "Select", "label": "Applicable For", "options": "\nCustomer\nCustomer Group\nTerritory\nSales Partner\nCampaign\nSupplier\nSupplier Group"}, {"depends_on": "eval:doc.applicable_for==\"Customer\"", "fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customer"}, {"depends_on": "eval:doc.applicable_for==\"Customer Group\"", "fieldname": "customer_group", "fieldtype": "Link", "label": "Customer Group", "options": "Customer Group"}, {"depends_on": "eval:doc.applicable_for==\"Territory\"", "fieldname": "territory", "fieldtype": "Link", "label": "Territory", "options": "Territory"}, {"depends_on": "eval:doc.applicable_for==\"Sales Partner\"", "fieldname": "sales_partner", "fieldtype": "Link", "label": "Sales Partner", "options": "Sales Partner"}, {"depends_on": "eval:doc.applicable_for==\"Campaign\"", "fieldname": "campaign", "fieldtype": "Link", "label": "Campaign", "options": "Campaign"}, {"depends_on": "eval:doc.applicable_for==\"Supplier\"", "fieldname": "supplier", "fieldtype": "Link", "label": "Supplier", "options": "Supplier"}, {"depends_on": "eval:doc.applicable_for==\"Supplier Group\"", "fieldname": "supplier_group", "fieldtype": "Link", "label": "Supplier Group", "options": "Supplier Group"}, {"fieldname": "section_break_19", "fieldtype": "Section Break", "label": "Quantity and Amount"}, {"fieldname": "min_qty", "fieldtype": "Float", "label": "<PERSON> (As Per Stock UOM)"}, {"fieldname": "max_qty", "fieldtype": "Float", "label": "<PERSON> (As Per Stock UOM)"}, {"fieldname": "column_break_21", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "min_amt", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON>", "options": "currency"}, {"default": "0", "fieldname": "max_amt", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON>", "options": "currency"}, {"fieldname": "section_break_23", "fieldtype": "Section Break", "label": "Period Settings"}, {"default": "Today", "fieldname": "valid_from", "fieldtype": "Date", "label": "<PERSON><PERSON>"}, {"fieldname": "valid_upto", "fieldtype": "Date", "label": "<PERSON><PERSON>"}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "remember_last_selected_value": 1}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "reqd": 1}, {"fieldname": "margin", "fieldtype": "Section Break", "label": "<PERSON><PERSON>"}, {"default": "Percentage", "fieldname": "margin_type", "fieldtype": "Select", "label": "Margin Type", "options": "\nPercentage\nAmount"}, {"fieldname": "column_break_33", "fieldtype": "Column Break"}, {"default": "0", "depends_on": "eval:doc.margin_type", "fieldname": "margin_rate_or_amount", "fieldtype": "Float", "label": "Margin Rate or Amount"}, {"depends_on": "eval:doc.price_or_product_discount == 'Price'", "fieldname": "price_discount_scheme_section", "fieldtype": "Section Break", "label": "Price Discount Scheme"}, {"default": "Discount Percentage", "fieldname": "rate_or_discount", "fieldtype": "Select", "label": "Rate or Discount", "options": "\nRate\nDiscount Percentage\nDiscount Amount"}, {"default": "Grand Total", "depends_on": "eval:doc.apply_on == 'Transaction' && doc.rate_or_discount != 'Rate'", "fieldname": "apply_discount_on", "fieldtype": "Select", "label": "Apply Discount On", "options": "Grand Total\nNet Total"}, {"fieldname": "col_break2", "fieldtype": "Column Break"}, {"default": "0", "depends_on": "eval:doc.rate_or_discount==\"Rate\"", "fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate", "options": "currency"}, {"default": "0", "depends_on": "eval:doc.rate_or_discount==\"Discount Amount\"", "fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Discount Amount", "options": "currency"}, {"depends_on": "eval:doc.rate_or_discount==\"Discount Percentage\"", "fieldname": "discount_percentage", "fieldtype": "Float", "label": "Discount Percentage"}, {"depends_on": "eval:doc.rate_or_discount!=\"Rate\"", "fieldname": "for_price_list", "fieldtype": "Link", "label": "For Price List", "options": "Price List"}, {"depends_on": "eval:doc.price_or_product_discount == 'Product'", "fieldname": "product_discount_scheme_section", "fieldtype": "Section Break", "label": "Product Discount Scheme"}, {"default": "0", "depends_on": "eval:!doc.mixed_conditions && doc.apply_on != 'Transaction'", "fieldname": "same_item", "fieldtype": "Check", "label": "Same Item"}, {"depends_on": "eval:(!doc.same_item || doc.apply_on == 'Transaction') || doc.mixed_conditions", "fieldname": "free_item", "fieldtype": "Link", "label": "Free Item", "options": "<PERSON><PERSON>"}, {"default": "0", "fieldname": "free_qty", "fieldtype": "Float", "label": "Qty"}, {"fieldname": "free_item_uom", "fieldtype": "Link", "label": "UOM", "options": "UOM"}, {"description": "If rate is zero then item will be treated as \"Free Item\"", "fieldname": "free_item_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Free Item Rate"}, {"collapsible": 1, "fieldname": "section_break_13", "fieldtype": "Tab Break", "label": "Advanced Settings"}, {"description": "System will notify to increase or decrease quantity or amount ", "fieldname": "threshold_percentage", "fieldtype": "Percent", "label": "Threshold for Suggestion (In Percentage)"}, {"depends_on": "has_priority", "description": "Higher the number, higher the priority", "fieldname": "priority", "fieldtype": "Select", "label": "Priority", "options": "\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20"}, {"fieldname": "column_break_66", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "apply_multiple_pricing_rules", "fieldtype": "Check", "label": "Apply Multiple Pricing Rules"}, {"default": "0", "depends_on": "eval:in_list(['Discount Percentage'], doc.rate_or_discount) && doc.apply_multiple_pricing_rules", "fieldname": "apply_discount_on_rate", "fieldtype": "Check", "label": "Apply Discount on Discounted Rate"}, {"default": "0", "depends_on": "eval:doc.price_or_product_discount == 'Price'", "description": "If enabled, then system will only validate the pricing rule and not apply automatically. User has to manually set the discount percentage / margin / free items to validate the pricing rule", "fieldname": "validate_applied_rule", "fieldtype": "Check", "label": "Validate Applied Rule"}, {"depends_on": "validate_applied_rule", "fieldname": "rule_description", "fieldtype": "Small Text", "label": "Rule Description"}, {"fieldname": "help_section", "fieldtype": "Tab Break", "label": "Help Article", "options": "Simple"}, {"fieldname": "pricing_rule_help", "fieldtype": "HTML", "label": "Pricing Rule Help"}, {"fieldname": "reference_section", "fieldtype": "Section Break", "hidden": 1, "label": "Reference"}, {"fieldname": "promotional_scheme_id", "fieldtype": "Data", "hidden": 1, "label": "Promotional Scheme Id", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "promotional_scheme", "fieldtype": "Link", "label": "Promotional Scheme", "no_copy": 1, "options": "Promotional Scheme", "print_hide": 1, "read_only": 1}, {"description": "Simple Python Expression, Example: territory != 'All Territories'", "fieldname": "condition", "fieldtype": "Code", "label": "Condition", "options": "PythonExpression"}, {"fieldname": "column_break_42", "fieldtype": "Column Break"}, {"default": "0", "description": "Discounts to be applied in sequential ranges like buy 1 get 1, buy 2 get 2, buy 3 get 3 and so on", "fieldname": "is_recursive", "fieldtype": "Check", "label": "Is Recursive"}, {"default": "PRLE-.####", "fieldname": "naming_series", "fieldtype": "Select", "label": "Naming Series", "options": "PRLE-.####"}, {"default": "0", "fieldname": "round_free_qty", "fieldtype": "Check", "label": "Round Free Qty"}, {"depends_on": "is_recursive", "description": "Give free item for every N quantity", "fieldname": "recurse_for", "fieldtype": "Float", "label": "Recurse Every (As Per Transaction UOM)", "mandatory_depends_on": "is_recursive"}, {"default": "0", "depends_on": "is_recursive", "description": "Qty for which recursion isn't applicable.", "fieldname": "apply_recursion_over", "fieldtype": "Float", "label": "Apply Recursion Over (As Per Transaction UOM)"}, {"fieldname": "priority_section", "fieldtype": "Section Break", "label": "Priority"}, {"fieldname": "dynamic_condition_tab", "fieldtype": "Tab Break", "label": "Dynamic Condition"}, {"fieldname": "validate_pricing_rule_section", "fieldtype": "Section Break", "label": "Validate Pricing Rule"}, {"fieldname": "column_break_texp", "fieldtype": "Column Break"}, {"fieldname": "column_break_sayg", "fieldtype": "Column Break"}, {"default": "0", "description": "Enable this checkbox even if you want to set the zero priority", "fieldname": "has_priority", "fieldtype": "Check", "label": "Has Priority"}, {"default": "0", "depends_on": "eval:doc.price_or_product_discount == 'Product'", "fieldname": "dont_enforce_free_item_qty", "fieldtype": "Check", "label": "Don't Enforce Free <PERSON><PERSON>"}], "icon": "fa fa-gift", "idx": 1, "links": [], "modified": "2025-02-17 18:15:39.824639", "modified_by": "Administrator", "module": "Accounts", "name": "Pricing Rule", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "export": 1, "import": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "read": 1, "report": 1, "role": "Purchase Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "read": 1, "report": 1, "role": "Website Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "export": 1, "import": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "title"}