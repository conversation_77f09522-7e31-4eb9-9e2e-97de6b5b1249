{"actions": [], "allow_rename": 1, "creation": "2021-12-09 15:44:58.033398", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["account", "account_name", "merged"], "fields": [{"columns": 4, "fieldname": "account", "fieldtype": "Link", "in_list_view": 1, "label": "Account", "options": "Account", "reqd": 1}, {"columns": 2, "default": "0", "fieldname": "merged", "fieldtype": "Check", "in_list_view": 1, "label": "<PERSON>rged", "read_only": 1}, {"columns": 4, "fieldname": "account_name", "fieldtype": "Data", "label": "Account Name", "read_only": 1, "reqd": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2021-12-10 15:27:24.477139", "modified_by": "Administrator", "module": "Accounts", "name": "Ledger <PERSON> Accounts", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC"}