{"actions": [], "creation": "2014-07-09 16:13:35.452759", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["reference_type", "reference_name", "posting_date", "is_advance", "reference_row", "col_break1", "amount", "difference_amount", "sec_break1", "remarks", "currency", "exchange_rate", "cost_center"], "fields": [{"fieldname": "reference_type", "fieldtype": "Link", "label": "Reference Type", "options": "DocType", "read_only": 1}, {"columns": 2, "fieldname": "reference_name", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Reference Name", "options": "reference_type", "read_only": 1}, {"fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "label": "Posting Date", "read_only": 1}, {"fieldname": "is_advance", "fieldtype": "Data", "hidden": 1, "label": "Is Advance", "read_only": 1}, {"fieldname": "reference_row", "fieldtype": "Data", "hidden": 1, "label": "Reference Row", "read_only": 1}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"columns": 2, "fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "options": "currency", "read_only": 1}, {"fieldname": "sec_break1", "fieldtype": "Section Break"}, {"fieldname": "currency", "fieldtype": "Link", "hidden": 1, "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "difference_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Difference Amount", "options": "currency", "read_only": 1}, {"fieldname": "exchange_rate", "fieldtype": "Float", "hidden": 1, "label": "Exchange Rate"}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks", "read_only": 1}], "is_virtual": 1, "istable": 1, "links": [], "modified": "2024-10-29 16:24:43.021230", "modified_by": "Administrator", "module": "Accounts", "name": "Payment Reconciliation Payment", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}