{"actions": [], "allow_import": 1, "allow_rename": 1, "creation": "2013-01-10 16:34:08", "description": "Standard tax template that can be applied to all Purchase Transactions. This template can contain a list of tax heads and also other expense heads like \"Shipping\", \"Insurance\", \"Handling\", etc.", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["title", "is_default", "disabled", "column_break4", "company", "tax_category", "section_break6", "taxes"], "fields": [{"fieldname": "title", "fieldtype": "Data", "label": "Title", "no_copy": 1, "oldfieldname": "title", "oldfieldtype": "Data", "reqd": 1}, {"default": "0", "fieldname": "is_default", "fieldtype": "Check", "in_list_view": 1, "label": "<PERSON><PERSON><PERSON>"}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "in_list_view": 1, "label": "Disabled"}, {"fieldname": "column_break4", "fieldtype": "Column Break"}, {"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Company", "options": "Company", "remember_last_selected_value": 1, "reqd": 1}, {"fieldname": "section_break6", "fieldtype": "Section Break"}, {"fieldname": "taxes", "fieldtype": "Table", "label": "Purchase Taxes and Charges", "oldfieldname": "purchase_tax_details", "oldfieldtype": "Table", "options": "Purchase Taxes and Charges"}, {"fieldname": "tax_category", "fieldtype": "Link", "label": "Tax Category", "options": "Tax Category"}], "icon": "fa fa-money", "idx": 1, "links": [], "modified": "2024-01-30 13:08:09.537242", "modified_by": "Administrator", "module": "Accounts", "name": "Purchase Taxes and Charges Template", "owner": "Administrator", "permissions": [{"email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase Manager"}, {"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase Master Manager", "share": 1, "write": 1}, {"read": 1, "role": "Purchase User"}], "show_title_field_in_link": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "title", "track_changes": 1}