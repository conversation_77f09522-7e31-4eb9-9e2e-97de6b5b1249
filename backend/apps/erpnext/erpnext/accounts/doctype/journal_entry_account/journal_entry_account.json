{"actions": [], "autoname": "hash", "creation": "2013-02-22 01:27:39", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["account", "account_type", "col_break1", "bank_account", "party_type", "party", "accounting_dimensions_section", "cost_center", "dimension_col_break", "project", "currency_section", "account_currency", "column_break_10", "exchange_rate", "sec_break1", "debit_in_account_currency", "debit", "col_break2", "credit_in_account_currency", "credit", "reference", "reference_type", "reference_name", "reference_due_date", "reference_detail_no", "advance_voucher_type", "advance_voucher_no", "col_break3", "is_advance", "user_remark", "against_account"], "fields": [{"bold": 1, "columns": 2, "fieldname": "account", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "label": "Account", "oldfieldname": "account", "oldfieldtype": "Link", "options": "Account", "print_width": "250px", "reqd": 1, "search_index": 1, "width": "250px"}, {"fieldname": "account_type", "fieldtype": "Data", "hidden": 1, "label": "Account Type", "print_hide": 1}, {"allow_on_submit": 1, "default": ":Company", "description": "If Income or Expense", "fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "oldfieldname": "cost_center", "oldfieldtype": "Link", "options": "Cost Center", "print_hide": 1, "print_width": "180px", "width": "180px"}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"fieldname": "party_type", "fieldtype": "Link", "in_list_view": 1, "label": "Party Type", "options": "DocType", "search_index": 1}, {"columns": 2, "fieldname": "party", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Party", "options": "party_type"}, {"fieldname": "currency_section", "fieldtype": "Section Break", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "account_currency", "fieldtype": "Link", "label": "Account <PERSON><PERSON><PERSON><PERSON>", "no_copy": 1, "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_10", "fieldtype": "Column Break"}, {"fieldname": "exchange_rate", "fieldtype": "Float", "label": "Exchange Rate", "precision": "9", "print_hide": 1}, {"fieldname": "sec_break1", "fieldtype": "Section Break", "label": "Amount"}, {"bold": 1, "columns": 2, "fieldname": "debit_in_account_currency", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Debit", "options": "account_currency", "print_hide_if_no_value": 1}, {"bold": 1, "fieldname": "debit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Debit in Company Currency", "no_copy": 1, "oldfieldname": "debit", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "col_break2", "fieldtype": "Column Break"}, {"bold": 1, "columns": 2, "fieldname": "credit_in_account_currency", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Credit", "options": "account_currency", "print_hide_if_no_value": 1}, {"bold": 1, "fieldname": "credit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Credit in Company Currency", "no_copy": 1, "oldfieldname": "credit", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "reference", "fieldtype": "Section Break", "label": "Reference"}, {"fieldname": "reference_type", "fieldtype": "Select", "label": "Reference Type", "no_copy": 1, "options": "\nSales Invoice\nPurchase Invoice\nJournal Entry\nSales Order\nPurchase Order\nExpense Claim\nAsset\nLoan\nPayroll Entry\nEmployee Advance\nExchange Rate Revaluation\nInvoice Discounting\nFees\nFull and Final Statement\nPayment Entry", "search_index": 1}, {"fieldname": "reference_name", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Reference Name", "no_copy": 1, "options": "reference_type", "search_index": 1}, {"depends_on": "eval:doc.reference_type&&!in_list(doc.reference_type, ['Expense Claim', 'Asset', 'Employee Loan', 'Employee Advance'])", "fieldname": "reference_due_date", "fieldtype": "Date", "label": "Reference Due Date", "no_copy": 1}, {"allow_on_submit": 1, "fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project"}, {"fieldname": "col_break3", "fieldtype": "Column Break"}, {"fieldname": "is_advance", "fieldtype": "Select", "label": "Is Advance", "no_copy": 1, "oldfieldname": "is_advance", "oldfieldtype": "Select", "options": "No\nYes", "print_hide": 1}, {"fieldname": "user_remark", "fieldtype": "Small Text", "label": "User Remark", "no_copy": 1, "print_hide": 1}, {"fieldname": "against_account", "fieldtype": "Text", "hidden": 1, "label": "Against Account", "no_copy": 1, "oldfieldname": "against_account", "oldfieldtype": "Text", "print_hide": 1}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"fieldname": "bank_account", "fieldtype": "Link", "label": "Bank Account", "options": "Bank Account"}, {"fieldname": "reference_detail_no", "fieldtype": "Data", "hidden": 1, "label": "Reference Detail No", "no_copy": 1, "search_index": 1}, {"fieldname": "advance_voucher_type", "fieldtype": "Link", "label": "Advance Voucher Type", "no_copy": 1, "options": "DocType", "read_only": 1}, {"fieldname": "advance_voucher_no", "fieldtype": "Dynamic Link", "label": "Advance Voucher No", "no_copy": 1, "options": "advance_voucher_type", "read_only": 1}], "idx": 1, "istable": 1, "links": [], "modified": "2025-07-25 04:45:28.117715", "modified_by": "Administrator", "module": "Accounts", "name": "Journal Entry Account", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}