{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:mode_of_payment", "creation": "2012-12-04 17:49:20", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["mode_of_payment", "enabled", "type", "accounts"], "fields": [{"fieldname": "mode_of_payment", "fieldtype": "Data", "in_list_view": 1, "label": "Mode of Payment", "oldfieldname": "mode_of_payment", "oldfieldtype": "Data", "reqd": 1, "unique": 1}, {"fieldname": "type", "fieldtype": "Select", "in_standard_filter": 1, "label": "Type", "options": "Cash\nBank\nGeneral\nPhone"}, {"fieldname": "accounts", "fieldtype": "Table", "label": "Accounts", "options": "Mode of Payment Account"}, {"default": "1", "fieldname": "enabled", "fieldtype": "Check", "label": "Enabled"}], "icon": "fa fa-credit-card", "idx": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2020-09-18 17:57:23.835236", "modified_by": "Administrator", "module": "Accounts", "name": "Mode of Payment", "owner": "Administrator", "permissions": [{"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"read": 1, "report": 1, "role": "Accounts User"}], "quick_entry": 1, "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "ASC"}