{"actions": [], "creation": "2020-01-28 11:56:33.945372", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["company", "posting_date", "posting_time", "merge_invoices_based_on", "column_break_3", "pos_closing_entry", "customer", "customer_group", "section_break_3", "pos_invoices", "references_section", "consolidated_invoice", "column_break_7", "consolidated_credit_note", "amended_from"], "fields": [{"fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "label": "Posting Date", "reqd": 1}, {"fieldname": "customer", "fieldtype": "Link", "in_list_view": 1, "label": "Customer", "options": "Customer", "reqd": 1}, {"fieldname": "section_break_3", "fieldtype": "Section Break"}, {"fieldname": "pos_invoices", "fieldtype": "Table", "label": "POS Invoices", "options": "POS Invoice Reference", "reqd": 1}, {"collapsible": 1, "fieldname": "references_section", "fieldtype": "Section Break", "label": "References"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "POS Invoice Merge Log", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "fieldname": "consolidated_invoice", "fieldtype": "Link", "label": "Consolidated Sales Invoice", "options": "Sales Invoice", "read_only": 1}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "fieldname": "consolidated_credit_note", "fieldtype": "Link", "label": "Consolidated Credit Note", "options": "Sales Invoice", "read_only": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "pos_closing_entry", "fieldtype": "Link", "label": "POS Closing Entry", "options": "POS Closing Entry"}, {"fieldname": "merge_invoices_based_on", "fieldtype": "Select", "label": "Merge Invoices Based On", "options": "Customer\nCustomer Group", "reqd": 1}, {"depends_on": "eval:doc.merge_invoices_based_on == 'Customer Group'", "fieldname": "customer_group", "fieldtype": "Link", "label": "Customer Group", "mandatory_depends_on": "eval:doc.merge_invoices_based_on == 'Customer Group'", "options": "Customer Group"}, {"fieldname": "posting_time", "fieldtype": "Time", "label": "Posting Time", "no_copy": 1, "reqd": 1}, {"fieldname": "company", "fieldtype": "Link", "in_standard_filter": 1, "label": "Company", "options": "Company", "print_hide": 1, "remember_last_selected_value": 1, "reqd": 1}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-07-02 17:08:04.747202", "modified_by": "Administrator", "module": "Accounts", "name": "POS Invoice Merge Log", "owner": "Administrator", "permissions": [{"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "submit": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Administrator", "share": 1, "submit": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1}