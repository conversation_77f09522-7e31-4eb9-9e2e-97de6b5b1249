{"actions": [], "allow_rename": 1, "creation": "2025-05-30 11:59:28.219277", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["source_currency", "pegged_against", "pegged_exchange_rate"], "fields": [{"fieldname": "source_currency", "fieldtype": "Link", "in_list_view": 1, "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "pegged_exchange_rate", "fieldtype": "Data", "in_list_view": 1, "label": "Exchange Rate"}, {"fieldname": "pegged_against", "fieldtype": "Link", "in_list_view": 1, "label": "Pegged Against", "options": "<PERSON><PERSON><PERSON><PERSON>"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-06-17 14:11:16.521193", "modified_by": "Administrator", "module": "Accounts", "name": "Pegged <PERSON><PERSON><PERSON>cy <PERSON>", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}