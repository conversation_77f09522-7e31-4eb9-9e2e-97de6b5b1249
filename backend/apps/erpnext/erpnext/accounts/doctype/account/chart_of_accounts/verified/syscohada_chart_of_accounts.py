import json
from pathlib import Path

syscohada_countries = [
	"bj",  # <PERSON><PERSON><PERSON>
	"bf",  # Burkina-Faso
	"cm",  # Cameroun
	"cf",  # Centrafrique
	"ci",  # Côte d'Ivoire
	"cg",  # Congo
	"km",  # Comores
	"ga",  # Gabon
	"gn",  # <PERSON><PERSON><PERSON>
	"gw",  # Guinée-Bissau
	"gq",  # <PERSON><PERSON><PERSON>
	"ml",  # Mali
	"ne",  # Niger
	"cd",  # République Démocratique du Congo
	"sn",  # Sénégal
	"td",  # Tchad
	"tg",  # Togo
]

folder = Path(__file__).parent
generic_charts = Path(folder).glob("syscohada*.json")

for file in generic_charts:
	with open(file) as f:
		chart = json.load(f)
	for country in syscohada_countries:
		chart["country_code"] = country
		json_object = json.dumps(chart, indent=4)
		with open(Path(folder, file.name.replace("syscohada", country)), "w") as outfile:
			outfile.write(json_object)
