{"actions": [], "allow_rename": 1, "autoname": "Prompt", "creation": "2013-05-24 12:15:51", "doctype": "DocType", "engine": "InnoDB", "field_order": ["company", "customer", "country", "disabled", "column_break_9", "warehouse", "campaign", "company_address", "section_break_15", "applicable_for_users", "section_break_11", "payments", "section_break_14", "hide_images", "hide_unavailable_items", "auto_add_item_to_cart", "validate_stock_on_save", "print_receipt_on_order_complete", "column_break_16", "update_stock", "ignore_pricing_rule", "allow_rate_change", "allow_discount_change", "disable_grand_total_to_default_mop", "allow_partial_payment", "section_break_23", "item_groups", "column_break_25", "customer_groups", "section_break_16", "print_format", "letter_head", "column_break0", "tc_name", "select_print_heading", "section_break_19", "selling_price_list", "currency", "write_off_account", "write_off_cost_center", "write_off_limit", "account_for_change_amount", "disable_rounded_total", "column_break_23", "income_account", "expense_account", "taxes_and_charges", "tax_category", "apply_discount_on", "accounting_dimensions_section", "cost_center", "dimension_col_break", "project"], "fields": [{"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"fieldname": "customer", "fieldtype": "Link", "label": "Customer", "oldfieldname": "customer_account", "oldfieldtype": "Link", "options": "Customer"}, {"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "remember_last_selected_value": 1, "reqd": 1}, {"fetch_from": "company.country", "fieldname": "country", "fieldtype": "Read Only", "label": "Country"}, {"fieldname": "campaign", "fieldtype": "Link", "label": "Campaign", "options": "Campaign"}, {"fieldname": "company_address", "fieldtype": "Link", "label": "Company Address", "options": "Address"}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "section_break_15", "fieldtype": "Section Break", "label": "Applicable for Users"}, {"fieldname": "applicable_for_users", "fieldtype": "Table", "label": "Applicable for Users", "options": "POS Profile User"}, {"fieldname": "section_break_11", "fieldtype": "Section Break", "label": "Payment Methods"}, {"fieldname": "payments", "fieldtype": "Table", "label": "Payment Methods", "options": "POS Payment Method", "reqd": 1}, {"fieldname": "section_break_14", "fieldtype": "Section Break", "label": "Configuration"}, {"description": "Only show Items from these Item Groups", "fieldname": "item_groups", "fieldtype": "Table", "label": "Item Groups", "options": "POS Item Group"}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"description": "Only show Customer of these Customer Groups", "fieldname": "customer_groups", "fieldtype": "Table", "label": "Customer Groups", "options": "POS Customer Group"}, {"fieldname": "section_break_16", "fieldtype": "Section Break", "label": "Print Settings"}, {"allow_on_submit": 1, "fieldname": "letter_head", "fieldtype": "Link", "label": "Letter Head", "oldfieldname": "letter_head", "oldfieldtype": "Select", "options": "Letter Head", "print_hide": 1}, {"fieldname": "column_break0", "fieldtype": "Column Break", "oldfieldtype": "Column Break"}, {"fieldname": "tc_name", "fieldtype": "Link", "label": "Terms and Conditions", "oldfieldname": "tc_name", "oldfieldtype": "Link", "options": "Terms and Conditions"}, {"allow_on_submit": 1, "fieldname": "select_print_heading", "fieldtype": "Link", "label": "Print Heading", "oldfieldname": "select_print_heading", "oldfieldtype": "Select", "options": "Print Heading"}, {"fieldname": "section_break_19", "fieldtype": "Section Break", "label": "Accounting"}, {"fieldname": "selling_price_list", "fieldtype": "Link", "label": "Price List", "oldfieldname": "price_list_name", "oldfieldtype": "Select", "options": "Price List"}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "oldfieldname": "currency", "oldfieldtype": "Select", "options": "<PERSON><PERSON><PERSON><PERSON>", "reqd": 1}, {"fieldname": "write_off_account", "fieldtype": "Link", "label": "Write Off Account", "options": "Account", "reqd": 1}, {"fieldname": "write_off_cost_center", "fieldtype": "Link", "label": "Write Off Cost Center", "options": "Cost Center", "reqd": 1}, {"fieldname": "account_for_change_amount", "fieldtype": "Link", "label": "Account for Change Amount", "options": "Account"}, {"fieldname": "column_break_23", "fieldtype": "Column Break"}, {"fieldname": "income_account", "fieldtype": "Link", "label": "Income Account", "oldfieldname": "income_account", "oldfieldtype": "Link", "options": "Account"}, {"fieldname": "expense_account", "fieldtype": "Link", "label": "Expense Account", "options": "Account", "print_hide": 1}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "oldfieldname": "cost_center", "oldfieldtype": "Link", "options": "Cost Center"}, {"fieldname": "taxes_and_charges", "fieldtype": "Link", "label": "Taxes and Charges", "oldfieldname": "charge", "oldfieldtype": "Link", "options": "Sales Taxes and Charges Template"}, {"default": "Grand Total", "fieldname": "apply_discount_on", "fieldtype": "Select", "label": "Apply Discount On", "options": "Grand Total\nNet Total"}, {"fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "tax_category", "fieldtype": "Link", "label": "Tax Category", "options": "Tax Category"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"fieldname": "print_format", "fieldtype": "Link", "label": "Print Format", "options": "Print Format"}, {"depends_on": "update_stock", "fieldname": "warehouse", "fieldtype": "Link", "label": "Warehouse", "oldfieldname": "warehouse", "oldfieldtype": "Link", "options": "Warehouse", "reqd": 1}, {"default": "0", "fieldname": "ignore_pricing_rule", "fieldtype": "Check", "label": "Ignore Pricing Rule"}, {"default": "1", "fieldname": "update_stock", "fieldtype": "Check", "hidden": 1, "label": "Update Stock", "read_only": 1}, {"default": "0", "fieldname": "hide_unavailable_items", "fieldtype": "Check", "label": "Hide Unavailable Items"}, {"default": "0", "fieldname": "hide_images", "fieldtype": "Check", "label": "Hide Images"}, {"default": "0", "fieldname": "auto_add_item_to_cart", "fieldtype": "Check", "label": "Automatically Add Filtered Item To Cart"}, {"default": "0", "fieldname": "allow_rate_change", "fieldtype": "Check", "label": "Allow User to Edit Rate"}, {"default": "0", "fieldname": "allow_discount_change", "fieldtype": "Check", "label": "Allow User to Edit Discount"}, {"fieldname": "section_break_23", "fieldtype": "Section Break", "label": "Filters"}, {"fieldname": "column_break_25", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "validate_stock_on_save", "fieldtype": "Check", "label": "Validate Stock on Save"}, {"default": "1", "description": "Auto write off precision loss while consolidation", "fieldname": "write_off_limit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Write Off Limit", "reqd": 1}, {"default": "0", "description": "If enabled, the consolidated invoices will have rounded total disabled", "fieldname": "disable_rounded_total", "fieldtype": "Check", "label": "Disable Rounded Total"}, {"default": "0", "fieldname": "print_receipt_on_order_complete", "fieldtype": "Check", "label": "Print Receipt on Order Complete"}, {"default": "0", "fieldname": "disable_grand_total_to_default_mop", "fieldtype": "Check", "label": "Disable auto setting Grand Total to default Payment Mode"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "oldfieldname": "cost_center", "oldfieldtype": "Link", "options": "Project"}, {"default": "0", "fieldname": "allow_partial_payment", "fieldtype": "Check", "label": "Allow Partial Payment"}], "icon": "icon-cog", "idx": 1, "index_web_pages_for_search": 1, "links": [{"group": "Invoices", "link_doctype": "Sales Invoice", "link_fieldname": "pos_profile"}, {"group": "Invoices", "link_doctype": "POS Invoice", "link_fieldname": "pos_profile"}, {"group": "Opening & Closing", "link_doctype": "POS Opening Entry", "link_fieldname": "pos_profile"}, {"group": "Opening & Closing", "link_doctype": "POS Closing Entry", "link_fieldname": "pos_profile"}], "modified": "2025-04-14 15:58:20.497426", "modified_by": "Administrator", "module": "Accounts", "name": "POS Profile", "naming_rule": "Set by user", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User"}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}