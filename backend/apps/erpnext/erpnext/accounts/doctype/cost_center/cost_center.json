{"actions": [], "allow_copy": 1, "allow_import": 1, "creation": "2013-01-23 19:57:17", "description": "Track separate Income and Expense for product verticals or divisions.", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["sb0", "cost_center_name", "cost_center_number", "parent_cost_center", "company", "cb0", "is_group", "disabled", "lft", "rgt", "old_parent"], "fields": [{"fieldname": "sb0", "fieldtype": "Section Break"}, {"fieldname": "cost_center_name", "fieldtype": "Data", "in_list_view": 1, "label": "Cost Center Name", "no_copy": 1, "oldfieldname": "cost_center_name", "oldfieldtype": "Data", "reqd": 1}, {"fieldname": "cost_center_number", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Cost Center Number", "read_only_depends_on": "eval:!doc.__islocal"}, {"fieldname": "parent_cost_center", "fieldtype": "Link", "ignore_user_permissions": 1, "in_list_view": 1, "label": "Parent Cost Center", "oldfieldname": "parent_cost_center", "oldfieldtype": "Link", "options": "Cost Center", "reqd": 1}, {"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Company", "oldfieldname": "company_name", "oldfieldtype": "Link", "options": "Company", "remember_last_selected_value": 1, "reqd": 1}, {"fieldname": "cb0", "fieldtype": "Column Break", "width": "50%"}, {"default": "0", "fieldname": "is_group", "fieldtype": "Check", "label": "Is Group"}, {"fieldname": "lft", "fieldtype": "Int", "hidden": 1, "label": "lft", "no_copy": 1, "oldfieldname": "lft", "oldfieldtype": "Int", "print_hide": 1, "report_hide": 1, "search_index": 1}, {"fieldname": "rgt", "fieldtype": "Int", "hidden": 1, "label": "rgt", "no_copy": 1, "oldfieldname": "rgt", "oldfieldtype": "Int", "print_hide": 1, "report_hide": 1, "search_index": 1}, {"fieldname": "old_parent", "fieldtype": "Link", "hidden": 1, "ignore_user_permissions": 1, "label": "old_parent", "no_copy": 1, "oldfieldname": "old_parent", "oldfieldtype": "Data", "options": "Cost Center", "print_hide": 1, "report_hide": 1}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}], "icon": "fa fa-money", "idx": 1, "is_tree": 1, "links": [], "modified": "2024-04-24 10:55:54.083042", "modified_by": "Administrator", "module": "Accounts", "name": "Cost Center", "nsm_parent_field": "parent_cost_center", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"export": 1, "read": 1, "report": 1, "role": "Auditor"}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User"}, {"read": 1, "role": "Sales User"}, {"read": 1, "role": "Purchase User"}, {"email": 1, "export": 1, "print": 1, "report": 1, "role": "Employee", "select": 1, "share": 1}], "search_fields": "parent_cost_center, is_group", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "ASC", "states": []}