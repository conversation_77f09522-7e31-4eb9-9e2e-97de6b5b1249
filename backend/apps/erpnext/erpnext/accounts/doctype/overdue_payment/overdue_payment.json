{"actions": [], "creation": "2021-09-15 18:34:27.172906", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["sales_invoice", "payment_schedule", "dunning_level", "payment_term", "section_break_15", "description", "section_break_4", "due_date", "overdue_days", "mode_of_payment", "column_break_5", "invoice_portion", "section_break_16", "payment_amount", "outstanding", "paid_amount", "discounted_amount", "interest"], "fields": [{"columns": 2, "fieldname": "payment_term", "fieldtype": "Link", "label": "Payment Term", "options": "Payment Term", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "section_break_15", "fieldtype": "Section Break", "label": "Description"}, {"columns": 2, "fetch_from": "payment_term.description", "fieldname": "description", "fieldtype": "Small Text", "label": "Description", "read_only": 1}, {"fieldname": "section_break_4", "fieldtype": "Section Break"}, {"columns": 2, "fieldname": "due_date", "fieldtype": "Date", "label": "Due Date", "read_only": 1}, {"fieldname": "mode_of_payment", "fieldtype": "Link", "label": "Mode of Payment", "options": "Mode of Payment", "read_only": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"columns": 2, "fieldname": "invoice_portion", "fieldtype": "Percent", "label": "Invoice Portion", "read_only": 1}, {"columns": 2, "fieldname": "payment_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Payment Amount", "options": "currency", "read_only": 1}, {"fetch_from": "payment_amount", "fieldname": "outstanding", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Outstanding", "options": "currency", "read_only": 1}, {"depends_on": "paid_amount", "fieldname": "paid_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON>", "options": "currency"}, {"default": "0", "depends_on": "discounted_amount", "fieldname": "discounted_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Discounted Amount", "print_hide": 1, "read_only": 1}, {"fieldname": "sales_invoice", "fieldtype": "Link", "in_list_view": 1, "label": "Sales Invoice", "options": "Sales Invoice", "read_only": 1, "reqd": 1}, {"fieldname": "payment_schedule", "fieldtype": "Data", "label": "Payment Schedule", "print_hide": 1, "read_only": 1}, {"fieldname": "overdue_days", "fieldtype": "Data", "in_list_view": 1, "label": "Overdue Days", "read_only": 1}, {"default": "1", "fieldname": "dunning_level", "fieldtype": "Int", "in_list_view": 1, "label": "Dunning Level", "read_only": 1}, {"fieldname": "section_break_16", "fieldtype": "Section Break"}, {"fieldname": "interest", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Interest", "options": "currency", "read_only": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2021-09-23 13:48:27.898830", "modified_by": "Administrator", "module": "Accounts", "name": "Overdue Payment", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}