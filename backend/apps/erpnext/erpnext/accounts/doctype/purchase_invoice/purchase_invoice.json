{"actions": [], "allow_import": 1, "autoname": "naming_series:", "creation": "2013-05-21 16:16:39", "doctype": "DocType", "document_type": "Document", "engine": "InnoDB", "field_order": ["title", "naming_series", "supplier", "supplier_name", "tax_id", "company", "column_break_6", "posting_date", "posting_time", "set_posting_time", "due_date", "column_break1", "is_paid", "is_return", "return_against", "update_outstanding_for_self", "update_billed_amount_in_purchase_order", "update_billed_amount_in_purchase_receipt", "apply_tds", "tax_withholding_category", "amended_from", "supplier_invoice_details", "bill_no", "column_break_15", "bill_date", "accounting_dimensions_section", "cost_center", "dimension_col_break", "project", "currency_and_price_list", "currency", "conversion_rate", "use_transaction_date_exchange_rate", "column_break2", "buying_price_list", "price_list_currency", "plc_conversion_rate", "ignore_pricing_rule", "sec_warehouse", "scan_barcode", "last_scanned_warehouse", "col_break_warehouse", "update_stock", "set_warehouse", "set_from_warehouse", "is_subcontracted", "rejected_warehouse", "supplier_warehouse", "items_section", "items", "section_break_26", "total_qty", "total_net_weight", "column_break_50", "base_total", "base_net_total", "column_break_28", "total", "net_total", "tax_withholding_net_total", "base_tax_withholding_net_total", "taxes_section", "tax_category", "taxes_and_charges", "column_break_58", "shipping_rule", "column_break_49", "incoterm", "named_place", "section_break_51", "taxes", "totals", "base_taxes_and_charges_added", "base_taxes_and_charges_deducted", "base_total_taxes_and_charges", "column_break_40", "taxes_and_charges_added", "taxes_and_charges_deducted", "total_taxes_and_charges", "section_break_49", "base_grand_total", "base_rounding_adjustment", "base_rounded_total", "base_in_words", "column_break8", "grand_total", "rounding_adjustment", "use_company_roundoff_cost_center", "rounded_total", "in_words", "total_advance", "outstanding_amount", "disable_rounded_total", "section_break_44", "apply_discount_on", "base_discount_amount", "column_break_46", "additional_discount_percentage", "discount_amount", "tax_withheld_vouchers_section", "tax_withheld_vouchers", "sec_tax_breakup", "other_charges_calculation", "pricing_rule_details", "pricing_rules", "raw_materials_supplied", "supplied_items", "payments_tab", "payments_section", "mode_of_payment", "base_paid_amount", "clearance_date", "col_br_payments", "cash_bank_account", "paid_amount", "advances_section", "allocate_advances_automatically", "only_include_allocated_payments", "get_advances", "advances", "advance_tax", "write_off", "write_off_amount", "base_write_off_amount", "column_break_61", "write_off_account", "write_off_cost_center", "address_and_contact_tab", "section_addresses", "supplier_address", "address_display", "col_break_address", "contact_person", "contact_display", "contact_mobile", "contact_email", "company_shipping_address_section", "dispatch_address", "dispatch_address_display", "column_break_126", "shipping_address", "shipping_address_display", "company_billing_address_section", "billing_address", "column_break_130", "billing_address_display", "terms_tab", "payment_schedule_section", "payment_terms_template", "ignore_default_payment_terms_template", "payment_schedule", "terms_section_break", "tc_name", "terms", "more_info_tab", "status_section", "status", "column_break_177", "per_received", "accounting_details_section", "credit_to", "party_account_currency", "is_opening", "against_expense_account", "column_break_63", "unrealized_profit_loss_account", "subscription_section", "subscription", "auto_repeat", "update_auto_repeat_reference", "column_break_114", "from_date", "to_date", "printing_settings", "letter_head", "group_same_items", "column_break_112", "select_print_heading", "language", "sb_14", "on_hold", "release_date", "cb_17", "hold_comment", "additional_info_section", "is_internal_supplier", "represents_company", "supplier_group", "column_break_147", "inter_company_invoice_reference", "is_old_subcontracting_flow", "remarks", "connections_tab"], "fields": [{"allow_on_submit": 1, "default": "{supplier_name}", "fieldname": "title", "fieldtype": "Data", "hidden": 1, "label": "Title", "no_copy": 1, "print_hide": 1}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "oldfieldname": "naming_series", "oldfieldtype": "Select", "options": "ACC-PINV-.YYYY.-\nACC-PINV-RET-.YYYY.-", "print_hide": 1, "reqd": 1, "set_only_once": 1}, {"fieldname": "supplier", "fieldtype": "Link", "in_standard_filter": 1, "label": "Supplier", "oldfieldname": "supplier", "oldfieldtype": "Link", "options": "Supplier", "print_hide": 1, "reqd": 1, "search_index": 1}, {"bold": 1, "depends_on": "supplier", "fetch_from": "supplier.supplier_name", "fieldname": "supplier_name", "fieldtype": "Data", "in_global_search": 1, "label": "Supplier Name", "oldfieldname": "supplier_name", "oldfieldtype": "Data", "read_only": 1}, {"fetch_from": "supplier.tax_id", "fieldname": "tax_id", "fieldtype": "Read Only", "label": "Tax Id", "print_hide": 1, "read_only": 1}, {"fieldname": "due_date", "fieldtype": "Date", "label": "Due Date", "oldfieldname": "due_date", "oldfieldtype": "Date"}, {"default": "0", "fieldname": "is_paid", "fieldtype": "Check", "label": "<PERSON>", "print_hide": 1}, {"default": "0", "fieldname": "is_return", "fieldtype": "Check", "label": "Is Return (Debit Note)", "no_copy": 1, "print_hide": 1}, {"default": "0", "fieldname": "apply_tds", "fieldtype": "Check", "label": "Apply Tax Withholding Amount", "print_hide": 1}, {"fieldname": "column_break1", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "width": "50%"}, {"fieldname": "company", "fieldtype": "Link", "in_standard_filter": 1, "label": "Company", "options": "Company", "print_hide": 1, "remember_last_selected_value": 1}, {"allow_on_submit": 1, "fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "label": "Date", "oldfieldname": "posting_date", "oldfieldtype": "Date", "print_hide": 1, "reqd": 1, "search_index": 1}, {"default": "Now", "fieldname": "posting_time", "fieldtype": "Time", "label": "Posting Time", "no_copy": 1, "print_hide": 1, "print_width": "100px", "width": "100px"}, {"default": "0", "depends_on": "eval:doc.docstatus==0", "fieldname": "set_posting_time", "fieldtype": "Check", "label": "Edit Posting Date and Time", "print_hide": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "oldfieldname": "amended_from", "oldfieldtype": "Link", "options": "Purchase Invoice", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "eval:doc.on_hold", "fieldname": "sb_14", "fieldtype": "Section Break", "label": "Hold Invoice"}, {"default": "0", "fieldname": "on_hold", "fieldtype": "Check", "label": "Hold Invoice"}, {"depends_on": "eval:doc.on_hold", "description": "Once set, this invoice will be on hold till the set date", "fieldname": "release_date", "fieldtype": "Date", "label": "Release Date", "search_index": 1}, {"fieldname": "cb_17", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.on_hold", "fieldname": "hold_comment", "fieldtype": "Small Text", "label": "Reason For Putting On Hold"}, {"collapsible": 1, "collapsible_depends_on": "bill_no", "fieldname": "supplier_invoice_details", "fieldtype": "Section Break", "label": "Supplier Invoice"}, {"fieldname": "bill_no", "fieldtype": "Data", "label": "Supplier Invoice No", "oldfieldname": "bill_no", "oldfieldtype": "Data", "print_hide": 1, "search_index": 1}, {"fieldname": "column_break_15", "fieldtype": "Column Break"}, {"fieldname": "bill_date", "fieldtype": "Date", "label": "Supplier Invoice Date", "no_copy": 1, "oldfieldname": "bill_date", "oldfieldtype": "Date", "print_hide": 1}, {"depends_on": "return_against", "fieldname": "return_against", "fieldtype": "Link", "label": "Return Against Purchase Invoice", "no_copy": 1, "options": "Purchase Invoice", "print_hide": 1, "read_only": 1, "search_index": 1}, {"default": "0", "depends_on": "eval: doc.is_return", "fieldname": "update_billed_amount_in_purchase_order", "fieldtype": "Check", "label": "Update Billed Amount in Purchase Order"}, {"default": "1", "depends_on": "eval: doc.is_return", "fieldname": "update_billed_amount_in_purchase_receipt", "fieldtype": "Check", "label": "Update Billed Amount in Purchase Receipt"}, {"fieldname": "section_addresses", "fieldtype": "Section Break", "label": "Supplier Address"}, {"fieldname": "supplier_address", "fieldtype": "Link", "label": "Select Supplier Address", "options": "Address", "print_hide": 1}, {"fieldname": "address_display", "fieldtype": "Small Text", "label": "Address", "read_only": 1}, {"fieldname": "contact_person", "fieldtype": "Link", "in_global_search": 1, "label": "Contact Person", "options": "Contact", "print_hide": 1}, {"fieldname": "contact_display", "fieldtype": "Small Text", "label": "Contact", "read_only": 1}, {"fieldname": "contact_mobile", "fieldtype": "Small Text", "label": "Mobile No", "options": "Phone", "read_only": 1}, {"fieldname": "contact_email", "fieldtype": "Small Text", "label": "Contact Email", "options": "Email", "print_hide": 1, "read_only": 1}, {"fieldname": "col_break_address", "fieldtype": "Column Break"}, {"fieldname": "shipping_address", "fieldtype": "Link", "label": "Select Shipping Address", "options": "Address", "print_hide": 1}, {"fieldname": "shipping_address_display", "fieldtype": "Small Text", "label": "Shipping Address", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "currency_and_price_list", "fieldtype": "Section Break", "label": "Currency and Price List", "options": "fa fa-tag"}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "oldfieldname": "currency", "oldfieldtype": "Select", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1}, {"fieldname": "conversion_rate", "fieldtype": "Float", "label": "Exchange Rate", "oldfieldname": "conversion_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "precision": "9", "print_hide": 1}, {"fieldname": "column_break2", "fieldtype": "Column Break"}, {"fieldname": "buying_price_list", "fieldtype": "Link", "label": "Price List", "options": "Price List", "print_hide": 1}, {"fieldname": "price_list_currency", "fieldtype": "Link", "label": "Price List Currency", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"fieldname": "plc_conversion_rate", "fieldtype": "Float", "label": "Price List Exchange Rate", "precision": "9", "print_hide": 1}, {"default": "0", "fieldname": "ignore_pricing_rule", "fieldtype": "Check", "label": "Ignore Pricing Rule", "permlevel": 1, "print_hide": 1}, {"fieldname": "sec_warehouse", "fieldtype": "Section Break", "hide_border": 1, "label": "Items"}, {"depends_on": "update_stock", "fieldname": "set_warehouse", "fieldtype": "Link", "label": "Set Accepted Warehouse", "options": "Warehouse", "print_hide": 1}, {"depends_on": "update_stock", "fieldname": "rejected_warehouse", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Rejected Warehouse", "no_copy": 1, "options": "Warehouse", "print_hide": 1}, {"fieldname": "col_break_warehouse", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "is_subcontracted", "fieldtype": "Check", "label": "Is Subcontracted", "print_hide": 1, "read_only": 1}, {"fieldname": "items_section", "fieldtype": "Section Break", "hide_border": 1, "oldfieldtype": "Section Break", "options": "fa fa-shopping-cart"}, {"default": "0", "fieldname": "update_stock", "fieldtype": "Check", "label": "Update Stock", "print_hide": 1}, {"fieldname": "scan_barcode", "fieldtype": "Data", "label": "Scan Barcode", "options": "Barcode"}, {"allow_bulk_edit": 1, "fieldname": "items", "fieldtype": "Table", "label": "Items", "oldfieldname": "entries", "oldfieldtype": "Table", "options": "Purchase Invoice Item", "reqd": 1}, {"collapsible": 1, "fieldname": "pricing_rule_details", "fieldtype": "Section Break", "label": "Pricing Rules"}, {"fieldname": "pricing_rules", "fieldtype": "Table", "label": "Pricing Rule Detail", "options": "Pricing Rule Detail", "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "supplied_items", "fieldname": "raw_materials_supplied", "fieldtype": "Section Break", "label": "Raw Materials Supplied"}, {"depends_on": "update_stock", "fieldname": "supplied_items", "fieldtype": "Table", "label": "Supplied Items", "no_copy": 1, "options": "Purchase Receipt Item Supplied"}, {"fieldname": "section_break_26", "fieldtype": "Section Break"}, {"fieldname": "total_qty", "fieldtype": "Float", "label": "Total Quantity", "read_only": 1}, {"fieldname": "base_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total (Company Currency)", "oldfieldname": "net_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_28", "fieldtype": "Column Break"}, {"fieldname": "total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total", "options": "currency", "read_only": 1}, {"fieldname": "net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total", "oldfieldname": "net_total_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "read_only": 1}, {"depends_on": "total_net_weight", "fieldname": "total_net_weight", "fieldtype": "Float", "label": "Total Net Weight", "print_hide": 1, "read_only": 1}, {"fieldname": "taxes_section", "fieldtype": "Section Break", "hide_border": 1, "label": "Taxes and Charges", "oldfieldtype": "Section Break", "options": "fa fa-money"}, {"fieldname": "tax_category", "fieldtype": "Link", "label": "Tax Category", "options": "Tax Category", "print_hide": 1}, {"fieldname": "column_break_49", "fieldtype": "Column Break"}, {"fieldname": "shipping_rule", "fieldtype": "Link", "label": "Shipping Rule", "options": "Shipping Rule", "print_hide": 1}, {"fieldname": "section_break_51", "fieldtype": "Section Break", "hide_border": 1}, {"fieldname": "taxes_and_charges", "fieldtype": "Link", "label": "Purchase Taxes and Charges Template", "oldfieldname": "purchase_other_charges", "oldfieldtype": "Link", "options": "Purchase Taxes and Charges Template", "print_hide": 1}, {"fieldname": "taxes", "fieldtype": "Table", "label": "Purchase Taxes and Charges", "oldfieldname": "purchase_tax_details", "oldfieldtype": "Table", "options": "Purchase Taxes and Charges"}, {"collapsible": 1, "fieldname": "sec_tax_breakup", "fieldtype": "Section Break", "label": "Tax Breakup"}, {"fieldname": "other_charges_calculation", "fieldtype": "Text Editor", "label": "Taxes and Charges Calculation", "no_copy": 1, "oldfieldtype": "HTML", "print_hide": 1, "read_only": 1}, {"fieldname": "totals", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "fa fa-money"}, {"fieldname": "base_taxes_and_charges_added", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Added (Company Currency)", "oldfieldname": "other_charges_added", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_taxes_and_charges_deducted", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Deducted (Company Currency)", "oldfieldname": "other_charges_deducted", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Taxes and Charges (Company Currency)", "oldfieldname": "total_tax", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_40", "fieldtype": "Column Break"}, {"fieldname": "taxes_and_charges_added", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Added", "oldfieldname": "other_charges_added_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "taxes_and_charges_deducted", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Deducted", "oldfieldname": "other_charges_deducted_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Taxes and Charges", "options": "currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "section_break_44", "fieldtype": "Section Break", "label": "Additional Discount"}, {"default": "Grand Total", "fieldname": "apply_discount_on", "fieldtype": "Select", "label": "Apply Additional Discount On", "options": "\nGrand Total\nNet Total", "print_hide": 1}, {"fieldname": "base_discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Discount Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_46", "fieldtype": "Column Break"}, {"fieldname": "additional_discount_percentage", "fieldtype": "Float", "label": "Additional Discount Percentage", "print_hide": 1}, {"fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Discount Amount", "options": "currency", "print_hide": 1}, {"fieldname": "section_break_49", "fieldtype": "Section Break", "label": "Totals"}, {"fieldname": "base_grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Grand Total (Company Currency)", "oldfieldname": "grand_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "base_rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounding Adjustment (Company Currency)", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "base_rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounded Total (Company Currency)", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_in_words", "fieldtype": "Data", "label": "In Words (Company Currency)", "length": 240, "oldfieldname": "in_words", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break8", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "print_hide": 1, "width": "50%"}, {"fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Grand Total", "oldfieldname": "grand_total_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounding Adjustment", "no_copy": 1, "options": "currency", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounded Total", "no_copy": 1, "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "in_words", "fieldtype": "Data", "label": "In Words", "length": 240, "oldfieldname": "in_words_import", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1}, {"fieldname": "total_advance", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Advance", "no_copy": 1, "oldfieldname": "total_advance", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "party_account_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "outstanding_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Outstanding Amount", "no_copy": 1, "oldfieldname": "outstanding_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "party_account_currency", "print_hide": 1, "read_only": 1}, {"default": "0", "depends_on": "grand_total", "fieldname": "disable_rounded_total", "fieldtype": "Check", "label": "Disable Rounded Total"}, {"collapsible": 1, "collapsible_depends_on": "paid_amount", "depends_on": "eval:doc.is_paid===1||(doc.advances && doc.advances.length>0)", "fieldname": "payments_section", "fieldtype": "Section Break", "label": "Payments"}, {"fieldname": "mode_of_payment", "fieldtype": "Link", "label": "Mode of Payment", "options": "Mode of Payment", "print_hide": 1}, {"allow_on_submit": 1, "fieldname": "cash_bank_account", "fieldtype": "Link", "label": "Cash/Bank Account", "options": "Account"}, {"fieldname": "clearance_date", "fieldtype": "Date", "label": "Clearance Date", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "col_br_payments", "fieldtype": "Column Break"}, {"depends_on": "is_paid", "fieldname": "paid_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON>", "no_copy": 1, "options": "currency", "print_hide": 1}, {"fieldname": "base_paid_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON> (Company Currency)", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "write_off", "fieldtype": "Section Break", "label": "Write Off"}, {"fieldname": "write_off_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Write Off Amount", "no_copy": 1, "options": "currency", "print_hide": 1}, {"fieldname": "base_write_off_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Write Off Amount (Company Currency)", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_61", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "depends_on": "eval:flt(doc.write_off_amount)!=0", "fieldname": "write_off_account", "fieldtype": "Link", "label": "Write Off Account", "options": "Account", "print_hide": 1}, {"depends_on": "eval:flt(doc.write_off_amount)!=0", "fieldname": "write_off_cost_center", "fieldtype": "Link", "label": "Write Off Cost Center", "options": "Cost Center", "print_hide": 1}, {"collapsible": 1, "collapsible_depends_on": "advances", "fieldname": "advances_section", "fieldtype": "Section Break", "label": "Advance Payments", "oldfieldtype": "Section Break", "options": "fa fa-money", "print_hide": 1}, {"default": "0", "fieldname": "allocate_advances_automatically", "fieldtype": "Check", "label": "Set Advances and Allocate (FIFO)"}, {"depends_on": "eval:!doc.allocate_advances_automatically", "fieldname": "get_advances", "fieldtype": "<PERSON><PERSON>", "label": "Get Advances Paid", "oldfieldtype": "<PERSON><PERSON>", "options": "set_advances", "print_hide": 1}, {"fieldname": "advances", "fieldtype": "Table", "label": "Advances", "no_copy": 1, "oldfieldname": "advance_allocation_details", "oldfieldtype": "Table", "options": "Purchase Invoice Advance", "print_hide": 1}, {"collapsible_depends_on": "eval:(!doc.is_return)", "fieldname": "payment_schedule_section", "fieldtype": "Section Break", "label": "Payment Terms"}, {"depends_on": "eval:(!doc.is_paid && !doc.is_return)", "fieldname": "payment_terms_template", "fieldtype": "Link", "label": "Payment Terms Template", "options": "Payment Terms Template"}, {"depends_on": "eval:(!doc.is_paid && !doc.is_return)", "fieldname": "payment_schedule", "fieldtype": "Table", "label": "Payment Schedule", "no_copy": 1, "options": "Payment Schedule", "print_hide": 1}, {"fieldname": "terms_section_break", "fieldtype": "Section Break", "label": "Terms and Conditions", "options": "fa fa-legal"}, {"fieldname": "tc_name", "fieldtype": "Link", "label": "Terms", "options": "Terms and Conditions", "print_hide": 1}, {"fieldname": "terms", "fieldtype": "Text Editor", "label": "Terms and Conditions"}, {"collapsible": 1, "fieldname": "printing_settings", "fieldtype": "Section Break", "label": "Print Settings"}, {"allow_on_submit": 1, "fieldname": "letter_head", "fieldtype": "Link", "label": "Letter Head", "options": "Letter Head", "print_hide": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "group_same_items", "fieldtype": "Check", "label": "Group same items", "print_hide": 1}, {"fieldname": "column_break_112", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "fieldname": "select_print_heading", "fieldtype": "Link", "label": "Print Heading", "no_copy": 1, "oldfieldname": "select_print_heading", "oldfieldtype": "Link", "options": "Print Heading", "print_hide": 1, "report_hide": 1}, {"fieldname": "language", "fieldtype": "Data", "label": "Print Language", "print_hide": 1, "read_only": 1}, {"default": "0", "fetch_from": "supplier.is_internal_supplier", "fieldname": "is_internal_supplier", "fieldtype": "Check", "ignore_user_permissions": 1, "label": "Is Internal Supplier", "read_only": 1}, {"fieldname": "credit_to", "fieldtype": "Link", "label": "Credit To", "oldfieldname": "credit_to", "oldfieldtype": "Link", "options": "Account", "print_hide": 1, "reqd": 1, "search_index": 1}, {"fieldname": "party_account_currency", "fieldtype": "Link", "hidden": 1, "label": "Party Account <PERSON><PERSON><PERSON><PERSON>", "no_copy": 1, "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "default": "No", "fieldname": "is_opening", "fieldtype": "Select", "label": "Is Opening Entry", "oldfieldname": "is_opening", "oldfieldtype": "Select", "options": "No\nYes", "print_hide": 1}, {"fieldname": "against_expense_account", "fieldtype": "Small Text", "hidden": 1, "label": "Against Expense Account", "no_copy": 1, "oldfieldname": "against_expense_account", "oldfieldtype": "Small Text", "print_hide": 1}, {"fieldname": "column_break_63", "fieldtype": "Column Break"}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "in_standard_filter": 1, "label": "Status", "no_copy": 1, "options": "\nDraft\nReturn\nDebit Note Issued\nSubmitted\nPaid\nPartly Paid\nUnpaid\nOverdue\nCancelled\nInternal Transfer", "print_hide": 1}, {"fieldname": "inter_company_invoice_reference", "fieldtype": "Link", "label": "Inter Company Invoice Reference", "no_copy": 1, "options": "Sales Invoice", "print_hide": 1, "read_only": 1}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks", "no_copy": 1, "oldfieldname": "remarks", "oldfieldtype": "Text", "print_hide": 1}, {"collapsible": 1, "fieldname": "subscription_section", "fieldtype": "Section Break", "label": "Subscription", "print_hide": 1}, {"allow_on_submit": 1, "description": "Start date of current invoice's period", "fieldname": "from_date", "fieldtype": "Date", "label": "From Date", "no_copy": 1, "print_hide": 1}, {"allow_on_submit": 1, "description": "End date of current invoice's period", "fieldname": "to_date", "fieldtype": "Date", "label": "To Date", "no_copy": 1, "print_hide": 1}, {"fieldname": "column_break_114", "fieldtype": "Column Break"}, {"fieldname": "auto_repeat", "fieldtype": "Link", "label": "Auto Repeat", "no_copy": 1, "options": "Auto Repeat", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "depends_on": "eval: doc.auto_repeat", "fieldname": "update_auto_repeat_reference", "fieldtype": "<PERSON><PERSON>", "label": "Update Auto Repeat Reference"}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions "}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"fieldname": "tax_withholding_category", "fieldtype": "Link", "hidden": 1, "label": "Tax Withholding Category", "options": "Tax Withholding Category", "print_hide": 1}, {"fieldname": "billing_address", "fieldtype": "Link", "label": "Select Billing Address", "options": "Address"}, {"fieldname": "billing_address_display", "fieldtype": "Small Text", "label": "Billing Address", "read_only": 1}, {"allow_on_submit": 1, "fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project"}, {"allow_on_submit": 1, "depends_on": "eval:doc.is_internal_supplier", "description": "Unrealized Profit/Loss account for intra-company transfers", "fieldname": "unrealized_profit_loss_account", "fieldtype": "Link", "label": "Unrealized Profit / Loss Account", "options": "Account"}, {"depends_on": "eval:doc.is_internal_supplier", "description": "Company which internal supplier represents", "fetch_from": "supplier.represents_company", "fieldname": "represents_company", "fieldtype": "Link", "label": "Represents Company", "options": "Company"}, {"depends_on": "eval:doc.update_stock && doc.is_internal_supplier", "fieldname": "set_from_warehouse", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Set From Warehouse", "no_copy": 1, "options": "Warehouse", "print_hide": 1, "print_width": "50px", "width": "50px"}, {"depends_on": "eval:doc.is_subcontracted", "fieldname": "supplier_warehouse", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Supplier Warehouse", "no_copy": 1, "options": "Warehouse", "print_hide": 1, "print_width": "50px", "width": "50px"}, {"fieldname": "per_received", "fieldtype": "Percent", "hidden": 1, "label": "Per Received", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"default": "0", "fieldname": "ignore_default_payment_terms_template", "fieldtype": "Check", "hidden": 1, "label": "Ignore Default Payment Terms Template", "read_only": 1}, {"collapsible": 1, "fieldname": "accounting_details_section", "fieldtype": "Section Break", "label": "Accounting Details", "print_hide": 1}, {"fieldname": "column_break_147", "fieldtype": "Column Break"}, {"fieldname": "advance_tax", "fieldtype": "Table", "hidden": 1, "label": "Advance Tax", "options": "Advance Tax", "read_only": 1}, {"fieldname": "subscription", "fieldtype": "Link", "label": "Subscription", "options": "Subscription"}, {"default": "0", "fieldname": "is_old_subcontracting_flow", "fieldtype": "Check", "hidden": 1, "label": "Is Old Subcontracting Flow", "read_only": 1}, {"default": "0", "depends_on": "apply_tds", "fieldname": "tax_withholding_net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "Tax Withholding Net Total", "no_copy": 1, "options": "currency", "read_only": 1}, {"depends_on": "apply_tds", "fieldname": "base_tax_withholding_net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "Base Tax Withholding Net Total", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"collapsible_depends_on": "tax_withheld_vouchers", "fieldname": "tax_withheld_vouchers_section", "fieldtype": "Section Break", "label": "Tax Withheld Vouchers"}, {"fieldname": "tax_withheld_vouchers", "fieldtype": "Table", "label": "Tax Withheld Vouchers", "no_copy": 1, "options": "Tax Withheld Vouchers", "read_only": 1}, {"fieldname": "payments_tab", "fieldtype": "Tab Break", "label": "Payments"}, {"fieldname": "address_and_contact_tab", "fieldtype": "Tab Break", "label": "Address & Contact"}, {"fieldname": "terms_tab", "fieldtype": "Tab Break", "label": "Terms"}, {"fieldname": "more_info_tab", "fieldtype": "Tab Break", "label": "More Info"}, {"fieldname": "connections_tab", "fieldtype": "Tab Break", "label": "Connections", "show_dashboard": 1}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"fieldname": "column_break_50", "fieldtype": "Column Break"}, {"fieldname": "column_break_58", "fieldtype": "Column Break"}, {"fieldname": "company_shipping_address_section", "fieldtype": "Section Break", "label": "Shipping Address"}, {"fieldname": "column_break_126", "fieldtype": "Column Break"}, {"fieldname": "company_billing_address_section", "fieldtype": "Section Break", "label": "Company Billing Address"}, {"fieldname": "column_break_130", "fieldtype": "Column Break"}, {"collapsible": 1, "fieldname": "status_section", "fieldtype": "Section Break", "label": "Status"}, {"fieldname": "column_break_177", "fieldtype": "Column Break"}, {"collapsible": 1, "fieldname": "additional_info_section", "fieldtype": "Section Break", "label": "Additional Info", "oldfieldtype": "Section Break", "options": "fa fa-file-text", "print_hide": 1}, {"fieldname": "incoterm", "fieldtype": "Link", "label": "Incoterm", "options": "Incoterm"}, {"depends_on": "incoterm", "fieldname": "named_place", "fieldtype": "Data", "label": "Named Place"}, {"default": "0", "depends_on": "allocate_advances_automatically", "description": "Advance payments allocated against orders will only be fetched", "fieldname": "only_include_allocated_payments", "fieldtype": "Check", "label": "Only Include Allocated Payments"}, {"default": "0", "fieldname": "use_company_roundoff_cost_center", "fieldtype": "Check", "label": "Use Company Default Round Off Cost Center"}, {"default": "0", "fieldname": "use_transaction_date_exchange_rate", "fieldtype": "Check", "label": "Use Transaction Date Exchange Rate", "read_only": 1}, {"fetch_from": "supplier.supplier_group", "fieldname": "supplier_group", "fieldtype": "Link", "label": "Supplier Group", "options": "Supplier Group"}, {"default": "1", "depends_on": "eval: doc.is_return && doc.return_against", "description": "Debit Note will update it's own outstanding amount, even if 'Return Against' is specified.", "fieldname": "update_outstanding_for_self", "fieldtype": "Check", "label": "Update Outstanding for Self"}, {"fieldname": "dispatch_address_display", "fieldtype": "Text Editor", "label": "Dispatch Address", "print_hide": 1, "read_only": 1}, {"fieldname": "dispatch_address", "fieldtype": "Link", "label": "Select Dispatch Address ", "options": "Address", "print_hide": 1}, {"depends_on": "eval: doc.last_scanned_warehouse", "fieldname": "last_scanned_warehouse", "fieldtype": "Data", "is_virtual": 1, "label": "Last Scanned Warehouse"}], "grid_page_length": 50, "icon": "fa fa-file-text", "idx": 204, "is_submittable": 1, "links": [], "modified": "2025-08-04 19:19:11.380664", "modified_by": "Administrator", "module": "Accounts", "name": "Purchase Invoice", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "submit": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase User"}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "submit": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Auditor"}, {"permlevel": 1, "read": 1, "role": "Accounts Manager", "write": 1}], "row_format": "Dynamic", "search_fields": "posting_date, supplier, bill_no, base_grand_total, outstanding_amount", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "timeline_field": "supplier", "title_field": "title", "track_changes": 1}