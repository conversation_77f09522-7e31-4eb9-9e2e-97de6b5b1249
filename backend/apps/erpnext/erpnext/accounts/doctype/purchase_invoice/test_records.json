[{"bill_no": "NA", "buying_price_list": "_Test Price List", "company": "_Test Company", "conversion_rate": 1, "credit_to": "_Test Payable - _TC", "currency": "INR", "doctype": "Purchase Invoice", "items": [{"amount": 500, "base_amount": 500, "base_rate": 50, "conversion_factor": 1.0, "cost_center": "_Test Cost Center - _TC", "doctype": "Purchase Invoice Item", "expense_account": "_Test Account Cost for Goods Sold - _TC", "item_code": "_Test Item Home Desktop 100", "item_name": "_Test Item Home Desktop 100", "item_tax_template": "_Test Account Excise Duty @ 10 - _TC", "parentfield": "items", "qty": 10, "rate": 50, "uom": "_Test UOM", "warehouse": "_Test Warehouse - _TC"}, {"amount": 750, "base_amount": 750, "base_rate": 150, "conversion_factor": 1.0, "cost_center": "_Test Cost Center - _TC", "doctype": "Purchase Invoice Item", "expense_account": "_Test Account Cost for Goods Sold - _TC", "item_code": "_Test Item Home Desktop 200", "item_name": "_Test Item Home Desktop 200", "parentfield": "items", "qty": 5, "rate": 150, "uom": "_Test UOM", "warehouse": "_Test Warehouse - _TC"}], "grand_total": 0, "naming_series": "T-PINV-", "taxes": [{"account_head": "_Test Account Shipping Charges - _TC", "add_deduct_tax": "Add", "category": "Valuation and Total", "charge_type": "Actual", "cost_center": "_Test Cost Center - _TC", "description": "Shipping Charges", "doctype": "Purchase Taxes and Charges", "parentfield": "taxes", "tax_amount": 100}, {"account_head": "_Test Account Customs Duty - _TC", "add_deduct_tax": "Add", "category": "Valuation", "charge_type": "On Net Total", "cost_center": "_Test Cost Center - _TC", "description": "Customs Duty", "doctype": "Purchase Taxes and Charges", "parentfield": "taxes", "rate": 10}, {"account_head": "_Test Account Excise Duty - _TC", "add_deduct_tax": "Add", "category": "Total", "charge_type": "On Net Total", "cost_center": "_Test Cost Center - _TC", "description": "Excise Duty", "doctype": "Purchase Taxes and Charges", "parentfield": "taxes", "rate": 12}, {"account_head": "_Test Account Education Cess - _TC", "add_deduct_tax": "Add", "category": "Total", "charge_type": "On Previous Row Amount", "cost_center": "_Test Cost Center - _TC", "description": "Education Cess", "doctype": "Purchase Taxes and Charges", "parentfield": "taxes", "rate": 2, "row_id": 3}, {"account_head": "_Test Account S&H Education Cess - _TC", "add_deduct_tax": "Add", "category": "Total", "charge_type": "On Previous Row Amount", "cost_center": "_Test Cost Center - _TC", "description": "S&H Education Cess", "doctype": "Purchase Taxes and Charges", "parentfield": "taxes", "rate": 1, "row_id": 3}, {"account_head": "_Test Account CST - _TC", "add_deduct_tax": "Add", "category": "Total", "charge_type": "On Previous Row Total", "cost_center": "_Test Cost Center - _TC", "description": "CST", "doctype": "Purchase Taxes and Charges", "parentfield": "taxes", "rate": 2, "row_id": 5}, {"account_head": "_Test Account VAT - _TC", "add_deduct_tax": "Add", "category": "Total", "charge_type": "On Net Total", "cost_center": "_Test Cost Center - _TC", "description": "VAT", "doctype": "Purchase Taxes and Charges", "parentfield": "taxes", "rate": 12.5}, {"account_head": "_Test Account Discount - _TC", "add_deduct_tax": "Deduct", "category": "Total", "charge_type": "On Previous Row Total", "cost_center": "_Test Cost Center - _TC", "description": "Discount", "doctype": "Purchase Taxes and Charges", "parentfield": "taxes", "rate": 10, "row_id": 7}], "supplier": "_Test Supplier", "supplier_name": "_Test Supplier"}, {"bill_no": "NA", "buying_price_list": "_Test Price List", "company": "_Test Company", "conversion_rate": 1.0, "credit_to": "_Test Payable - _TC", "currency": "INR", "doctype": "Purchase Invoice", "items": [{"conversion_factor": 1.0, "cost_center": "_Test Cost Center - _TC", "doctype": "Purchase Invoice Item", "expense_account": "_Test Account Cost for Goods Sold - _TC", "item_code": "_Test Item", "item_name": "_Test Item", "parentfield": "items", "qty": 10.0, "rate": 50.0, "uom": "_Test UOM"}], "grand_total": 0, "naming_series": "T-PINV-", "taxes": [{"account_head": "_Test Account Shipping Charges - _TC", "add_deduct_tax": "Add", "category": "Valuation and Total", "charge_type": "Actual", "cost_center": "_Test Cost Center - _TC", "description": "Shipping Charges", "doctype": "Purchase Taxes and Charges", "parentfield": "taxes", "tax_amount": 100.0}, {"account_head": "_Test Account VAT - _TC", "add_deduct_tax": "Add", "category": "Total", "charge_type": "Actual", "cost_center": "_Test Cost Center - _TC", "description": "VAT", "doctype": "Purchase Taxes and Charges", "parentfield": "taxes", "tax_amount": 120.0}, {"account_head": "_Test Account Customs Duty - _TC", "add_deduct_tax": "Add", "category": "Valuation", "charge_type": "Actual", "cost_center": "_Test Cost Center - _TC", "description": "Customs Duty", "doctype": "Purchase Taxes and Charges", "parentfield": "taxes", "tax_amount": 150.0}], "supplier": "_Test Supplier", "supplier_name": "_Test Supplier"}]