{"actions": [], "allow_rename": 1, "creation": "2024-10-16 16:57:12.085072", "doctype": "DocType", "engine": "InnoDB", "field_order": ["company", "voucher_type", "voucher_no", "against_voucher_type", "against_voucher_no", "amount", "currency", "event", "delinked"], "fields": [{"fieldname": "voucher_type", "fieldtype": "Link", "label": "Voucher Type", "options": "DocType", "read_only": 1}, {"fieldname": "voucher_no", "fieldtype": "Dynamic Link", "label": "Voucher No", "options": "voucher_type", "read_only": 1}, {"fieldname": "against_voucher_type", "fieldtype": "Link", "label": "Against Voucher Type", "options": "DocType", "read_only": 1}, {"fieldname": "against_voucher_no", "fieldtype": "Dynamic Link", "label": "Against Voucher No", "options": "against_voucher_type", "read_only": 1}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount", "read_only": 1}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "event", "fieldtype": "Data", "label": "Event", "read_only": 1}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "read_only": 1}, {"default": "0", "fieldname": "delinked", "fieldtype": "Check", "label": "DeLinked", "read_only": 1}], "grid_page_length": 50, "in_create": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-29 11:37:42.678556", "modified_by": "Administrator", "module": "Accounts", "name": "Advance Payment Ledger Entry", "owner": "Administrator", "permissions": [{"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Auditor", "share": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}