# Copyright (c) 2025, Safari Tour Company and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document

class Comment(Document):
	def validate(self):
		"""Validate the Comment document"""
		self.validate_user_exists()
		self.validate_reference()
		self.validate_parent_comment()
	
	def validate_user_exists(self):
		"""Ensure the user exists"""
		# Skip validation if user is None (happens during system operations)
		if not self.user:
			return
		if not frappe.db.exists("User", self.user):
			frappe.throw(f"User {self.user} does not exist")
	
	def validate_reference(self):
		"""Validate the reference document exists"""
		# Skip validation if reference fields are not set (happens during system operations)
		if not self.reference_type or not self.reference_id:
			return
		if not frappe.db.exists(self.reference_type, self.reference_id):
			frappe.throw(f"{self.reference_type} {self.reference_id} does not exist")
	
	def validate_parent_comment(self):
		"""Validate parent comment if specified"""
		if self.parent_comment:
			if not frappe.db.exists("Comment", self.parent_comment):
				frappe.throw(f"Parent comment {self.parent_comment} does not exist")
			
			# Ensure parent comment is for the same reference
			parent = frappe.get_doc("Comment", self.parent_comment)
			if parent.reference_type != self.reference_type or parent.reference_id != self.reference_id:
				frappe.throw("Parent comment must be for the same reference")
	
	def after_insert(self):
		"""Actions after inserting the comment"""
		self.update_reference_stats()
		self.update_parent_stats()
	
	def on_update(self):
		"""Actions on comment update"""
		self.update_stats()
	
	def update_stats(self):
		"""Update comment statistics"""
		# Update likes count
		self.likes_count = frappe.db.count("Like", {
			"reference_type": "Comment",
			"reference_id": self.name
		})
		
		# Update replies count
		self.replies_count = frappe.db.count("Comment", {
			"parent_comment": self.name
		})
		
		# Save without triggering hooks
		frappe.db.set_value("Comment", self.name, {
			"likes_count": self.likes_count,
			"replies_count": self.replies_count
		})
	
	def update_reference_stats(self):
		"""Update statistics of the referenced document"""
		if self.reference_type in ["Post", "Product", "Tour"]:
			ref_doc = frappe.get_doc(self.reference_type, self.reference_id)
			if hasattr(ref_doc, 'update_stats'):
				ref_doc.update_stats()
	
	def update_parent_stats(self):
		"""Update parent comment statistics"""
		if self.parent_comment:
			parent = frappe.get_doc("Comment", self.parent_comment)
			parent.update_stats()

@frappe.whitelist()
def get_comments(reference_type, reference_id, limit=50, offset=0):
	"""Get comments for a reference document"""
	comments = frappe.get_all("Comment",
		filters={
			"reference_type": reference_type,
			"reference_id": reference_id,
			"status": "Active",
			"parent_comment": ["is", "not set"]
		},
		fields=["name", "content", "user", "creation", "likes_count", "replies_count"],
		limit=limit,
		start=offset,
		order_by="creation asc"
	)
	
	# Get user profiles and replies for each comment
	for comment in comments:
		# Get user profile
		user_profile = frappe.db.get_value("User Profile", comment.user, 
			["avatar_url"], as_dict=True)
		comment["user_profile"] = user_profile or {}
		
		# Get replies
		replies = frappe.get_all("Comment",
			filters={
				"parent_comment": comment.name,
				"status": "Active"
			},
			fields=["name", "content", "user", "creation", "likes_count"],
			order_by="creation asc"
		)
		
		# Get user profiles for replies
		for reply in replies:
			reply_user_profile = frappe.db.get_value("User Profile", reply.user, 
				["avatar_url"], as_dict=True)
			reply["user_profile"] = reply_user_profile or {}
		
		comment["replies"] = replies
	
	return comments

@frappe.whitelist()
def create_comment(comment_data):
	"""Create a new comment"""
	comment_data["doctype"] = "Comment"
	comment_data["user"] = frappe.session.user
	
	comment = frappe.get_doc(comment_data)
	comment.insert()
	
	return comment.as_dict()

@frappe.whitelist()
def update_comment(comment_id, content):
	"""Update comment content"""
	comment = frappe.get_doc("Comment", comment_id)
	
	# Check if user owns the comment
	if comment.user != frappe.session.user:
		frappe.throw("You can only edit your own comments")
	
	comment.content = content
	comment.is_edited = 1
	comment.save()
	
	return comment.as_dict()

@frappe.whitelist()
def delete_comment(comment_id):
	"""Delete a comment (soft delete)"""
	comment = frappe.get_doc("Comment", comment_id)
	
	# Check if user owns the comment or is admin
	if comment.user != frappe.session.user and not frappe.has_permission("Comment", "delete"):
		frappe.throw("You can only delete your own comments")
	
	comment.status = "Deleted"
	comment.save()
	
	return {"message": "Comment deleted successfully"}
