# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backend specific ignores
backend/env/
backend/logs/
backend/sites/*/private/backups/*
backend/sites/*/private/files/*
backend/sites/*/public/files/*
backend/sites/*/logs/*
backend/sites/*/site_config.json
backend/sites/*/locks/*
backend/sites/assets/*
backend/sites/.assets_cache
backend/config/pids/*
backend/__pycache__/
backend/**/__pycache__/
backend/**/*.pyc
backend/**/*.pyo
backend/**/*.pyd
backend/**/*.so
backend/**/*.egg-info/

# Frontend specific ignores
frontend/node_modules/
frontend/dist/
frontend/build/
frontend/.cache/
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log
logs/
